# CAHProxy - Comprehensive Codebase Documentation

## Project Overview

**CAHProxy** is a Velocity proxy plugin for the CraftAndHelps Minecraft server network. It serves as a centralized authentication, player data management, and VIP system for a multi-server Minecraft network. The plugin is built using Java 17 and the Velocity API 3.4.0.

## Architecture Overview

The plugin follows a modular architecture with clear separation of concerns:

### Core Components
1. **Main Plugin Class** - Entry point and event coordination
2. **Database Management** - MySQL connection pooling and data persistence
3. **Player Data System** - Centralized player data management with caching
4. **Authentication System** - Login/registration with password hashing
5. **VIP Management** - VIP code system with expiration handling
6. **Achievement System** - Achievement tracking and rewards
7. **Punishment System** - Ban/mute management
8. **Command System** - Various administrative and player commands

## Technology Stack

- **Java Version**: 17
- **Build Tool**: Maven
- **Primary Framework**: Velocity API 3.4.0-SNAPSHOT
- **Database**: MySQL with HikariCP connection pooling
- **Configuration**: YAML (SnakeYAML)
- **Shading**: Maven Shade Plugin with dependency relocation

## Database Schema

The plugin expects the following MySQL tables:

### `players` Table
- `uuid` (VARCHAR) - Player UUID (Primary Key)
- `username` (VARCHAR) - Player username
- `password_hash` (VARCHAR) - SHA-256 hashed password
- `coins` (BIGINT) - Player coins
- `money` (BIGINT) - Player money
- `almas` (BIGINT) - Player souls/almas
- `rank_id` (INT) - Player rank ID
- `blocks_mined` (BIGINT) - Total blocks mined
- `kills` (BIGINT) - Player kills
- `deaths` (BIGINT) - Player deaths
- `prestige` (INT) - Player prestige level
- `twitch_channel` (VARCHAR) - Associated Twitch channel
- `group_id` (INT) - Permission group ID
- `vip_expiration` (BIGINT) - VIP expiration timestamp
- `language` (VARCHAR) - Player language preference

### `punishments` Table
- `player_uuid` (VARCHAR) - Target player UUID
- `staff_uuid` (VARCHAR) - Staff member UUID
- `punishment_type` (VARCHAR) - BAN or MUTE
- `reason` (VARCHAR) - Punishment reason
- `expires_at` (TIMESTAMP) - Expiration time (NULL for permanent)
- `active` (BOOLEAN) - Whether punishment is active
- `remover_uuid` (VARCHAR) - UUID of staff who removed punishment
- `removed_at` (TIMESTAMP) - When punishment was removed

### `vip_codes` Table
- `code` (VARCHAR) - VIP code (Primary Key)
- `vip_type` (VARCHAR) - VIP type (vip, vip+, vippro, vipsuper, vipgod)
- `duration` (VARCHAR) - Duration string (e.g., "30d", "perma")
- `created_at` (TIMESTAMP) - Creation timestamp

### Achievement Tables
- `achievement_progress` - Player progress on achievements
- `achievement_completions` - Completed achievements with timestamps

## Core Classes Deep Dive

### Main.java
**Purpose**: Plugin entry point and event coordinator
**Key Features**:
- Initializes all managers and systems
- Handles plugin message communication between proxy and backend servers
- Manages player data synchronization via plugin channels
- Coordinates achievement system communication
- Handles server kick events for lobby redirection

**Plugin Channels**:
- `cah:playerdata` - Player data synchronization
- `ach:main` - Achievement system communication

### DatabaseManager.java
**Purpose**: Database connection and basic operations
**Key Features**:
- HikariCP connection pooling for performance
- Basic player data CRUD operations
- Connection management and cleanup
- Hardcoded database credentials (should be externalized)

**Connection Details**:
- Database: `craftandhelps`
- Host: `localhost:3306`
- Username: `root`
- Password: `19877@CraftAndHelps@2023`

### PlayerDataManager.java
**Purpose**: Centralized player data management with caching
**Key Features**:
- In-memory caching using ConcurrentHashMap
- Asynchronous data loading and saving
- Automatic player creation for new users
- Thread-safe operations
- Delta-based updates from backend servers

**Data Flow**:
1. Player joins → Load data from database → Cache in memory
2. Backend servers send deltas → Apply to cached data
3. Player leaves → Save to database → Remove from cache

### LoginManager.java
**Purpose**: Authentication system
**Key Features**:
- SHA-256 password hashing
- Registration and login validation
- Session management with in-memory tracking
- Password change functionality
- Automatic lobby redirection after login

**Security**:
- Passwords are hashed using SHA-256
- No plaintext password storage
- Session-based authentication tracking

### VIPManager.java
**Purpose**: VIP system management
**Key Features**:
- VIP code generation and validation
- Time-based VIP expiration
- Automatic permission group assignment
- Player kicking for permission updates
- Periodic expiration checking (every 5 minutes)

**VIP Types and Group IDs**:
- `vip` → Group ID 2
- `vip+` → Group ID 3
- `vippro` → Group ID 4
- `vipsuper` → Group ID 5
- `vipgod` → Group ID 6

### AchievementManager.java
**Purpose**: Achievement system management
**Key Features**:
- YAML-based achievement definitions
- Player progress tracking
- Completion detection and rewards
- Backend server synchronization
- Command execution on achievement completion

**Achievement Categories**:
- Bronze, Silver, Gold, Platinum, Mythic
- Each with different reward tiers
- Progress thresholds and command rewards

### BanManager.java
**Purpose**: Punishment system
**Key Features**:
- Ban and mute management
- Temporary and permanent punishments
- Staff tracking for punishments
- Active punishment checking
- Punishment removal/pardoning

## Command System

### Administrative Commands
- `/banir` - Ban players
- `/desbanir` - Unban players  
- `/mutar` - Mute players
- `/desmutar` - Unmute players
- `/kick` - Kick players
- `/vip` - VIP management (multiple subcommands)

### Player Commands
- `/login` - Player authentication
- `/register` - Account registration
- `/changepassword` - Password modification
- `/idioma` - Language selection
- `/hub` - Return to lobby

### VIP Command Subcommands
- `addcode` - Create VIP codes
- `removecode` - Remove VIP codes
- `settime` - Set VIP duration
- `give` - Grant VIP directly
- `check` - Check VIP status
- `codes` - View active codes count
- `redeem` - Redeem VIP codes

## Permission System

**Group ID Hierarchy**:
- 1: Default/Member
- 2-6: VIP tiers
- 12+: Administrative permissions

**Command Permissions**:
- Most admin commands require Group ID 12+
- VIP redeem/check available to all players
- Console has full permissions for shop integration

## Data Synchronization

### Plugin Message System
The plugin uses Velocity's plugin messaging to synchronize data between proxy and backend servers:

1. **Player Connection**: Proxy sends full player data to backend server
2. **Data Updates**: Backend servers send delta updates to proxy
3. **Achievement Updates**: Bidirectional achievement progress sync

### Delta System
Backend servers can send partial updates (deltas) to modify specific player data fields without full data replacement.

## Event Handling

### Key Event Listeners
- **LoginListener**: Handles authentication flow and restrictions
- **BanListener**: Enforces punishments
- **VIPListener**: Manages VIP expiration checks

### Event Flow
1. Player joins → Load data → Check authentication
2. Player switches servers → Send data to new server
3. Player leaves → Save data → Cleanup cache
4. Periodic tasks → Check VIP expiration

## Configuration Files

### achievements.yml
Defines all achievements with:
- Unique IDs and display names
- Description and icon materials
- Progress thresholds
- Reward commands
- Category classification

## Security Considerations

### Current Security Measures
- SHA-256 password hashing
- SQL injection prevention via PreparedStatements
- Session-based authentication
- Command permission checking

### Security Concerns
- Database credentials hardcoded in source
- No rate limiting on authentication attempts
- No password complexity requirements
- No encryption for plugin message data

## Performance Optimizations

### Caching Strategy
- In-memory player data caching
- Achievement data caching
- VIP code caching
- Concurrent data structures for thread safety

### Database Optimizations
- HikariCP connection pooling
- Prepared statement reuse
- Batch operations for achievements
- Asynchronous database operations

## Integration Points

### Backend Server Integration
- Plugin message channels for data sync
- Achievement progress reporting
- Delta-based updates
- Command execution from achievements

### External System Integration
- Shop system integration via console commands
- Twitch channel association
- Leaderboard systems (referenced in achievements)

## Monitoring and Logging

### Logging Strategy
- SLF4J logging framework
- Detailed operation logging
- Error handling with stack traces
- Performance monitoring for database operations

### Key Metrics Logged
- Player data load/save operations
- VIP expiration events
- Achievement completions
- Authentication attempts
- Database connection issues

## Deployment Considerations

### Build Process
- Maven-based build with shade plugin
- Dependency relocation to avoid conflicts
- Resource filtering for version templating

### Runtime Requirements
- Java 17+ runtime
- MySQL database server
- Velocity proxy server
- Sufficient memory for player data caching

## Future Enhancement Opportunities

### Scalability Improvements
- Redis integration for distributed caching
- Database connection pooling optimization
- Horizontal scaling support

### Feature Enhancements
- Web-based administration panel
- Advanced punishment system
- Enhanced achievement system
- Multi-language support expansion

### Security Enhancements
- External configuration for database credentials
- Rate limiting implementation
- Enhanced password policies
- Audit logging system

## Troubleshooting Guide

### Common Issues
1. **Database Connection Failures**: Check MySQL server status and credentials
2. **Player Data Not Loading**: Verify database schema and permissions
3. **VIP Not Working**: Check group ID assignments and expiration times
4. **Achievement Sync Issues**: Verify plugin message channels are registered

### Debug Information
- Enable debug logging for detailed operation traces
- Monitor database connection pool status
- Check plugin message channel registration
- Verify player data cache consistency

This documentation provides a comprehensive overview of the CAHProxy codebase, its architecture, and operational characteristics. The system is designed for a multi-server Minecraft network with centralized player management, authentication, and VIP systems.
